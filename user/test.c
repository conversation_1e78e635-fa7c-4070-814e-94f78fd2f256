#include "user.h"

int main(void)
{
    printf("Hello from User Program 3!\n");

    // 测试系统调用
    int pid = getpid();
    printf("System call test - getpid() returned: ");

    char pid_str[10];
    itoa(pid, pid_str);
    printf(pid_str);
    printf("\n");

    // 测试字符串操作
    printf("String operations test:\n");
    char msg[] = "Hello World!";
    printf("Message: ");
    printf(msg);
    printf("\n");

    printf("Message length: ");
    int len = strlen(msg);
    char len_str[10];
    itoa(len, len_str);
    printf(len_str);
    printf("\n");

    printf("Test program completed successfully!\n");

    exit(0);
    return 0;
}
